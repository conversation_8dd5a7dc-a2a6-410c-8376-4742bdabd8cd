import { useState, useCallback, useEffect } from 'react';

// 存储键常量
const HIGHLIGHT_STORAGE_KEY = 'gupiao-stock-highlights-v1';

/**
 * 股票高亮状态管理Hook的返回类型
 */
interface UseStockHighlightReturn {
  /** 当前高亮的股票代码集合 */
  highlights: Set<string>;
  /** 切换单个股票的高亮状态 */
  toggleHighlight: (code: string) => void;
  /** 检查股票是否被高亮 */
  isHighlighted: (code: string) => boolean;
  /** 清除所有高亮状态 */
  clearAllHighlights: () => void;
  /** 批量切换多个股票的高亮状态 */
  toggleMultiple: (codes: string[]) => void;
}

/**
 * 股票高亮状态管理Hook
 * 
 * 提供股票高亮标记功能，支持：
 * - localStorage持久化存储
 * - 单个股票高亮状态切换
 * - 批量操作
 * - 页面刷新后状态保持
 * 
 * @returns 高亮状态管理对象
 */
export function useStockHighlight(): UseStockHighlightReturn {
  const [highlights, setHighlights] = useState<Set<string>>(new Set());

  // 从localStorage加载初始状态
  useEffect(() => {
    try {
      const saved = localStorage.getItem(HIGHLIGHT_STORAGE_KEY);
      if (saved) {
        const highlightArray = JSON.parse(saved) as string[];
        if (Array.isArray(highlightArray)) {
          setHighlights(new Set(highlightArray));
        }
      }
    } catch (error) {
      console.warn('加载股票高亮状态失败:', error);
      // 如果加载失败，使用空的Set，不影响功能
      setHighlights(new Set());
    }
  }, []);

  // 保存状态到localStorage
  const saveToStorage = useCallback((newHighlights: Set<string>) => {
    try {
      const highlightArray = Array.from(newHighlights);
      localStorage.setItem(HIGHLIGHT_STORAGE_KEY, JSON.stringify(highlightArray));
    } catch (error) {
      console.warn('保存股票高亮状态失败:', error);
      // localStorage失败不影响内存中的状态
    }
  }, []);

  // 切换单个股票的高亮状态
  const toggleHighlight = useCallback((code: string) => {
    if (!code || typeof code !== 'string') {
      console.warn('无效的股票代码:', code);
      return;
    }

    setHighlights(prev => {
      const newSet = new Set(prev);
      if (newSet.has(code)) {
        newSet.delete(code);
      } else {
        newSet.add(code);
      }
      
      // 异步保存到localStorage，避免阻塞UI
      setTimeout(() => saveToStorage(newSet), 0);
      
      return newSet;
    });
  }, [saveToStorage]);

  // 检查股票是否被高亮
  const isHighlighted = useCallback((code: string): boolean => {
    if (!code || typeof code !== 'string') {
      return false;
    }
    return highlights.has(code);
  }, [highlights]);

  // 清除所有高亮状态
  const clearAllHighlights = useCallback(() => {
    setHighlights(new Set());
    // 异步清除localStorage
    setTimeout(() => {
      try {
        localStorage.removeItem(HIGHLIGHT_STORAGE_KEY);
      } catch (error) {
        console.warn('清除股票高亮状态失败:', error);
      }
    }, 0);
  }, []);

  // 批量切换多个股票的高亮状态
  const toggleMultiple = useCallback((codes: string[]) => {
    if (!Array.isArray(codes) || codes.length === 0) {
      return;
    }

    setHighlights(prev => {
      const newSet = new Set(prev);
      codes.forEach(code => {
        if (code && typeof code === 'string') {
          if (newSet.has(code)) {
            newSet.delete(code);
          } else {
            newSet.add(code);
          }
        }
      });
      
      // 异步保存到localStorage
      setTimeout(() => saveToStorage(newSet), 0);
      
      return newSet;
    });
  }, [saveToStorage]);

  return {
    highlights,
    toggleHighlight,
    isHighlighted,
    clearAllHighlights,
    toggleMultiple,
  };
}

export default useStockHighlight;
