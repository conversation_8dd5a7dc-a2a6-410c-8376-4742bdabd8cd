import React, { useMemo, useState } from 'react';
import { useQueryClient } from 'react-query';
import { useStockList } from '@/hooks/useStockList';
import { useBatchStockData } from '@/hooks/useStockData';
import { detectVPattern } from '@/utils/patternDetection';
import { StockMonitorItem } from './StockMonitorItem';
import { RefreshCw, AlertCircle, Activity, TrendingUp, BarChart3 } from 'lucide-react';
import { QUERY_KEYS } from '@/utils/queryClient';

interface StockMonitorPanelProps {
  /** 是否为紧凑模式 */
  compact?: boolean;
  /** 最大显示数量 */
  maxItems?: number;
  /** 点击股票的处理函数 */
  onStockClick?: (stockCode: string) => void;
  /** 自定义类名 */
  className?: string;
}

/**
 * 股票监控面板组件
 * 实时显示所有股票的资金流入状况，自动识别V字型模式
 */
export const StockMonitorPanel: React.FC<StockMonitorPanelProps> = ({
  compact = false,
  maxItems = 20,
  onStockClick,
  className = '',
}) => {
  const queryClient = useQueryClient();
  const [sortBy, setSortBy] = useState<'vPattern' | 'flow' | 'change'>('vPattern');
  
  // 获取股票列表
  const { stocks } = useStockList();
  const stockCodes = stocks.map(s => s.code);

  // 批量获取股票数据，每分钟自动刷新
  const { 
    results, 
    isLoading, 
    isError, 
    error, 
    refetch,
    isFetching,
    successCount,
    errorCount 
  } = useBatchStockData(stockCodes, 20, {
    // 使用统一配置的刷新间隔（3分钟，从60秒调整）
    enabled: stockCodes.length > 0
  });

  // 处理股票数据，集成V字型识别
  const stocksWithData = useMemo(() => {
    if (!results || Object.keys(results).length === 0) {
      return [];
    }

    const processedStocks = stocks.map(stock => {
      const data = results[stock.code];
      const hasVPattern = data?.klines ? detectVPattern(data.klines).hasVPattern : false;
      
      // 计算最新流入和变化
      const latestFlow = data?.klines?.[data.klines.length - 1]?.mainNetInflow || 0;
      const change24h = data?.klines && data.klines.length >= 2 
        ? data.klines[data.klines.length - 1].mainNetInflow - data.klines[data.klines.length - 2].mainNetInflow
        : 0;

      return {
        ...stock,
        data,
        hasVPattern,
        latestFlow,
        change24h,
      };
    }).filter(stock => stock.data); // 只显示有数据的股票

    // 排序逻辑
    return processedStocks.sort((a, b) => {
      switch (sortBy) {
        case 'vPattern':
          // V字型股票排在前面，然后按资金流入排序
          if (a.hasVPattern && !b.hasVPattern) return -1;
          if (!a.hasVPattern && b.hasVPattern) return 1;
          return Math.abs(b.latestFlow) - Math.abs(a.latestFlow);
        
        case 'flow':
          // 按资金流入绝对值排序
          return Math.abs(b.latestFlow) - Math.abs(a.latestFlow);
        
        case 'change':
          // 按24小时变化排序
          return Math.abs(b.change24h) - Math.abs(a.change24h);
        
        default:
          return 0;
      }
    }).slice(0, maxItems); // 限制显示数量
  }, [results, stocks, sortBy, maxItems]);

  // 统计信息
  const stats = useMemo(() => {
    const vPatternCount = stocksWithData.filter(s => s.hasVPattern).length;
    const positiveFlowCount = stocksWithData.filter(s => s.latestFlow > 0).length;
    const totalStocks = stocksWithData.length;
    
    return {
      total: totalStocks,
      vPattern: vPatternCount,
      positiveFlow: positiveFlowCount,
      negativeFlow: totalStocks - positiveFlowCount,
    };
  }, [stocksWithData]);

  // 处理全局刷新
  const handleRefresh = () => {
    refetch();
  };

  // 处理单个股票刷新 - 优化版本，只刷新单个股票数据
  const handleStockRefresh = async (stockCode: string) => {
    try {
      // 只使单个股票的查询失效并重新获取
      await queryClient.invalidateQueries(QUERY_KEYS.STOCK_DATA(stockCode));

      // 移除批量查询刷新，让智能缓存复用机制处理数据一致性
      // 这样可以避免刷新单个股票时触发所有股票的数据重新获取

      console.log(`已刷新股票 ${stockCode} 的数据`);
    } catch (error) {
      console.error(`刷新股票 ${stockCode} 数据失败:`, error);
    }
  };

  // 处理排序切换
  const handleSortChange = (newSort: typeof sortBy) => {
    setSortBy(newSort);
  };

  // 加载状态
  if (isLoading && stockCodes.length > 0) {
    return (
      <div className={`space-y-3 ${className}`}>
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <RefreshCw className="w-6 h-6 animate-spin text-blue-500 mx-auto mb-2" />
            <p className="text-sm text-gray-600">正在加载监控数据...</p>
            <p className="text-xs text-gray-500 mt-1">
              {stockCodes.length} 只股票
            </p>
          </div>
        </div>
      </div>
    );
  }

  // 错误状态
  if (isError) {
    return (
      <div className={`space-y-3 ${className}`}>
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <AlertCircle className="w-6 h-6 text-red-500 mx-auto mb-2" />
            <p className="text-sm text-red-600 mb-2">监控数据加载失败</p>
            <p className="text-xs text-gray-500 mb-3">
              {error?.message || '网络连接异常'}
            </p>
            <button
              onClick={handleRefresh}
              className="text-xs bg-blue-500 text-white px-3 py-1 rounded hover:bg-blue-600 transition-colors"
            >
              重新加载
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 无股票状态
  if (stockCodes.length === 0) {
    return (
      <div className={`space-y-3 ${className}`}>
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <BarChart3 className="w-6 h-6 text-gray-400 mx-auto mb-2" />
            <p className="text-sm text-gray-600">暂无监控股票</p>
            <p className="text-xs text-gray-500 mt-1">
              请先添加股票到监控列表
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* 头部信息和控制 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h3 className={`font-medium text-gray-900 ${compact ? 'text-sm' : 'text-base'}`}>
            实时监控
          </h3>
          {isFetching && (
            <RefreshCw className="w-3 h-3 animate-spin text-blue-500" />
          )}
        </div>
        
        <button
          onClick={handleRefresh}
          className="text-xs text-gray-500 hover:text-gray-700 transition-colors"
          disabled={isFetching}
        >
          刷新
        </button>
      </div>

      {/* 统计信息 */}
      {!compact && (
        <div className="grid grid-cols-4 gap-2 text-center">
          <div className="bg-gray-50 rounded p-2">
            <div className="text-xs text-gray-500">总计</div>
            <div className="text-sm font-bold text-gray-900">{stats.total}</div>
          </div>
          <div className="bg-red-50 rounded p-2">
            <div className="text-xs text-red-600 flex items-center justify-center gap-1">
              <Activity className="w-3 h-3" />
              V型
            </div>
            <div className="text-sm font-bold text-red-600">{stats.vPattern}</div>
          </div>
          <div className="bg-green-50 rounded p-2">
            <div className="text-xs text-green-600 flex items-center justify-center gap-1">
              <TrendingUp className="w-3 h-3" />
              流入
            </div>
            <div className="text-sm font-bold text-green-600">{stats.positiveFlow}</div>
          </div>
          <div className="bg-blue-50 rounded p-2">
            <div className="text-xs text-blue-600">成功</div>
            <div className="text-sm font-bold text-blue-600">{successCount}</div>
          </div>
        </div>
      )}

      {/* 排序控制 */}
      {!compact && stocksWithData.length > 0 && (
        <div className="flex gap-1">
          {[
            { key: 'vPattern', label: 'V型优先' },
            { key: 'flow', label: '资金流入' },
            { key: 'change', label: '24h变化' },
          ].map(({ key, label }) => (
            <button
              key={key}
              onClick={() => handleSortChange(key as typeof sortBy)}
              className={`text-xs px-2 py-1 rounded transition-colors ${
                sortBy === key
                  ? 'bg-blue-500 text-white'
                  : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
              }`}
            >
              {label}
            </button>
          ))}
        </div>
      )}

      {/* 股票监控列表 */}
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {stocksWithData.map(stock => (
          <StockMonitorItem
            key={stock.code}
            stock={stock}
            data={stock.data}
            hasVPattern={stock.hasVPattern}
            onClick={onStockClick}
            compact={compact}
            onRefresh={handleStockRefresh}
          />
        ))}
      </div>

      {/* 底部状态 */}
      {stocksWithData.length > 0 && (
        <div className="text-xs text-gray-500 text-center pt-2 border-t">
          显示 {stocksWithData.length} / {stockCodes.length} 只股票
          {errorCount > 0 && (
            <span className="text-red-500 ml-2">
              · {errorCount} 只加载失败
            </span>
          )}
        </div>
      )}
    </div>
  );
};

export default StockMonitorPanel;
