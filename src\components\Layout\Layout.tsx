import React, { useState, useEffect } from 'react';
import { Sidebar } from './Sidebar';

interface LayoutProps {
  /** 子组件 */
  children?: React.ReactNode;
}

/**
 * 应用主布局组件
 */
export const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // 检测移动端
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);



  // 移动端自动关闭侧边栏
  useEffect(() => {
    if (isMobile) {
      setIsSidebarOpen(false);
    }
  }, [isMobile]);

  // 处理股票选择 - 修改为直接在新标签页打开东方财富资金流向页面
  const handleStockSelect = (code: string) => {
    // 直接在新标签页打开东方财富资金流向页面
    const url = `https://data.eastmoney.com/zjlx/${code}.html`;
    window.open(url, '_blank');

    // 移动端选择后关闭侧边栏
    if (isMobile) {
      setIsSidebarOpen(false);
    }
  };



  return (
    <div className="min-h-screen bg-gray-50">
      {/* 主要内容区域 - 全屏股票管理模块 */}
      <div className="relative h-screen w-full overflow-hidden">
        {/* 股票管理模块 - 占据整个页面 */}
        <div className="absolute inset-0 w-full h-full overflow-hidden">
          <Sidebar
            selectedStock={null}
            onStockSelect={handleStockSelect}
            isOpen={isMobile ? isSidebarOpen : true}
            onClose={isMobile ? () => setIsSidebarOpen(false) : undefined}
            className="w-full h-full relative transform-none"
          />
        </div>

        {/* 弹出窗口功能已移除 - 现在直接在新标签页打开东方财富资金流向页面 */}
      </div>
    </div>
  );
};
