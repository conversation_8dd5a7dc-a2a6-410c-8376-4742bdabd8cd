import { useQuery, useMutation, useQueryClient } from 'react-query';
import {
  stockDataApi,
  stockManagementApi
} from '@/services/stockApi';
import { 
  QUERY_KEYS, 
  QUERY_OPTIONS,
  clearStockCache 
} from '@/utils/queryClient';
import {
  UseStockDataResult,
  UseBatchStockDataResult,
  UseStockListResult,
  UseApiStatusResult,
  StockFlowSummary,
  KlineDataPoint
} from '@/types/stock';

/**
 * 获取单个股票数据的Hook
 * @param code 股票代码
 * @param limit 数据条数
 * @param options 查询选项
 */
export function useStockData(
  code: string,
  limit: number = 240,
  options: {
    enabled?: boolean;
    useCache?: boolean;
    refetchInterval?: number;
  } = {}
): UseStockDataResult {
  const { enabled = true, useCache = true, refetchInterval } = options;

  const query = useQuery(
    QUERY_KEYS.STOCK_DATA(code),
    () => stockDataApi.getStockData(code, limit, useCache),
    {
      ...QUERY_OPTIONS.STOCK_DATA,
      enabled: enabled && !!code,
      refetchInterval: refetchInterval ?? QUERY_OPTIONS.STOCK_DATA.refetchInterval,
    }
  );

  const data = query.data;
  const summary: StockFlowSummary | null = data?.summary || null;
  const klines: KlineDataPoint[] = data?.klines || [];
  const lastUpdate: string | null = summary?.lastUpdate || null;

  return {
    ...query,
    data: query.data,
    summary,
    klines,
    lastUpdate,
    isLoading: query.isLoading,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isFetching: query.isFetching,
    isRefetching: query.isRefetching,
    error: query.error as Error | null,
    refetch: query.refetch,
    remove: query.remove,
  };
}

/**
 * 批量获取股票数据的Hook - 优化版本，支持智能缓存复用
 * @param codes 股票代码数组
 * @param limit 数据条数
 * @param options 查询选项
 */
export function useBatchStockData(
  codes: string[],
  limit: number = 240,
  options: {
    enabled?: boolean;
    useCache?: boolean;
    refetchInterval?: number;
    fallbackToIndividual?: boolean;
  } = {}
): UseBatchStockDataResult {
  const { enabled = true, useCache = true, refetchInterval, fallbackToIndividual = true } = options;
  const queryClient = useQueryClient();

  const query = useQuery(
    QUERY_KEYS.STOCK_DATA_BATCH(codes),
    async () => {
      if (codes.length === 0) {
        return {
          results: {},
          errors: {},
          summary: {
            total: 0,
            success: 0,
            failed: 0,
            fromCache: 0,
          },
        };
      }

      // 智能缓存复用：检查每个股票的单个缓存状态
      const results: Record<string, any> = {};
      const errors: Record<string, string> = {};
      const codesToFetch: string[] = [];
      let successCount = 0;
      let failedCount = 0;
      let fromCacheCount = 0;

      // 检查缓存中已有的数据
      for (const code of codes) {
        const cachedData = queryClient.getQueryData(QUERY_KEYS.STOCK_DATA(code));
        const queryState = queryClient.getQueryState(QUERY_KEYS.STOCK_DATA(code));

        // 如果缓存存在且未过期（5分钟内），直接使用缓存
        if (cachedData && queryState &&
            queryState.dataUpdatedAt &&
            (Date.now() - queryState.dataUpdatedAt) < 5 * 60 * 1000) {
          results[code] = cachedData;
          successCount++;
          fromCacheCount++;
        } else {
          // 需要重新获取的股票
          codesToFetch.push(code);
        }
      }

      // 如果所有数据都在缓存中，直接返回
      if (codesToFetch.length === 0) {
        console.log(`批量查询：所有 ${codes.length} 个股票数据来自缓存`);
        return {
          results,
          errors,
          summary: {
            total: codes.length,
            success: successCount,
            failed: failedCount,
            fromCache: fromCacheCount,
          },
        };
      }

      console.log(`批量查询：${codesToFetch.length}/${codes.length} 个股票需要重新获取`);

      try {
        // 首先尝试批量请求需要更新的股票
        const batchResult = await stockDataApi.getBatchStockData(codesToFetch, limit, useCache);

        // 合并批量请求结果
        Object.assign(results, batchResult.results);
        Object.assign(errors, batchResult.errors);
        successCount += batchResult.summary.success;
        failedCount += batchResult.summary.failed;
        fromCacheCount += batchResult.summary.fromCache;

        return {
          results,
          errors,
          summary: {
            total: codes.length,
            success: successCount,
            failed: failedCount,
            fromCache: fromCacheCount,
          },
        };
      } catch (error) {
        console.warn('批量请求失败，尝试降级策略:', error);

        // 如果启用了降级策略且股票数量不太多，尝试单个请求
        if (fallbackToIndividual && codesToFetch.length <= 30) {
          console.log('执行降级策略：单个股票请求');

          // 限制并发数量
          const CONCURRENT_LIMIT = 5;

          for (let i = 0; i < codesToFetch.length; i += CONCURRENT_LIMIT) {
            const batch = codesToFetch.slice(i, i + CONCURRENT_LIMIT);
            const promises = batch.map(async (code) => {
              try {
                const data = await stockDataApi.getStockData(code, limit, useCache);
                results[code] = data;
                successCount++;
              } catch (err) {
                errors[code] = err instanceof Error ? err.message : '获取失败';
                failedCount++;
              }
            });

            await Promise.all(promises);

            // 添加延迟避免频率限制
            if (i + CONCURRENT_LIMIT < codes.length) {
              await new Promise(resolve => setTimeout(resolve, 100));
            }
          }

          console.log(`降级策略完成：成功${successCount}个，失败${failedCount}个`);

          return {
            results,
            errors,
            summary: {
              total: codes.length,
              success: successCount,
              failed: failedCount,
              fromCache: 0,
            },
          };
        }

        // 如果不能降级，重新抛出错误
        throw error;
      }
    },
    {
      ...QUERY_OPTIONS.STOCK_DATA_BATCH,
      enabled: enabled && codes.length > 0,
      refetchInterval: refetchInterval ?? QUERY_OPTIONS.STOCK_DATA_BATCH.refetchInterval,
      // 增加重试次数和延迟
      retry: (failureCount, error) => {
        // 对于400错误（如股票数量超限），不重试
        if (error && typeof error === 'object' && 'status' in error && error.status === 400) {
          return false;
        }
        // 其他错误最多重试2次
        return failureCount < 2;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    }
  );

  const data = query.data;
  const results = data?.results || {};
  const errors = data?.errors || {};
  const summary = data?.summary || { total: 0, success: 0, failed: 0, fromCache: 0 };

  return {
    ...query,
    data: query.data,
    results,
    errors,
    successCount: summary.success,
    errorCount: summary.failed,
    fromCacheCount: summary.fromCache,
    isLoading: query.isLoading,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isFetching: query.isFetching,
    isRefetching: query.isRefetching,
    error: query.error as Error | null,
    refetch: query.refetch,
    remove: query.remove,
  };
}

/**
 * 股票列表管理Hook
 */
export function useStockList(): UseStockListResult {
  const queryClient = useQueryClient();

  const query = useQuery(
    QUERY_KEYS.STOCK_LIST,
    stockManagementApi.getStocks,
    QUERY_OPTIONS.STOCK_LIST
  );

  // 添加股票变更
  const addStockMutation = useMutation(
    ({ code, name }: { code: string; name?: string }) => 
      stockManagementApi.addStock(code, name),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(QUERY_KEYS.STOCK_LIST);
      },
    }
  );

  // 删除股票变更 - 优化版本，避免触发不必要的批量查询刷新
  const removeStockMutation = useMutation(
    (code: string) => stockManagementApi.removeStock(code),
    {
      onSuccess: (_, code) => {
        // 只刷新股票列表，不触发批量数据查询的刷新
        queryClient.invalidateQueries(QUERY_KEYS.STOCK_LIST);

        // 清除被删除股票的相关缓存
        clearStockCache(code);

        // 注意：不再刷新批量查询，让智能缓存复用机制处理
        // 这样删除股票时，其他股票的数据可以继续使用缓存
      },
    }
  );

  // 批量添加股票变更
  const addStocksBatchMutation = useMutation(
    (stocks: Array<{ code: string; name?: string }>) => 
      stockManagementApi.addStocksBatch(stocks),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(QUERY_KEYS.STOCK_LIST);
      },
    }
  );

  // 清空所有股票变更
  const clearAllStocksMutation = useMutation(
    stockManagementApi.clearAllStocks,
    {
      onSuccess: () => {
        queryClient.invalidateQueries(QUERY_KEYS.STOCK_LIST);
        queryClient.clear(); // 清除所有缓存
      },
    }
  );

  const stocks = query.data || [];

  return {
    ...query,
    data: query.data,
    stocks,
    isLoading: query.isLoading || addStockMutation.isLoading || removeStockMutation.isLoading,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isFetching: query.isFetching,
    isRefetching: query.isRefetching,
    error: query.error as Error | null,
    refetch: query.refetch,
    remove: query.remove,
    addStock: async (code: string, name?: string) => {
      await addStockMutation.mutateAsync({ code, name });
    },
    removeStock: async (code: string) => {
      await removeStockMutation.mutateAsync(code);
    },
    addStocksBatch: async (stocks: Array<{ code: string; name?: string }>) => {
      await addStocksBatchMutation.mutateAsync(stocks);
    },
    clearAllStocks: async () => {
      await clearAllStocksMutation.mutateAsync();
    },
  };
}

/**
 * API服务状态Hook
 */
export function useApiStatus(): UseApiStatusResult {
  const query = useQuery(
    QUERY_KEYS.API_STATUS,
    stockDataApi.getServiceStatus,
    QUERY_OPTIONS.API_STATUS
  );

  const data = query.data;
  const isHealthy = data?.isHealthy || false;
  const canMakeRequest = data?.rateLimitStatus?.canMakeRequest || false;
  const nextAvailableTime = data?.rateLimitStatus?.nextAvailableTime || 0;

  return {
    ...query,
    data: query.data,
    isHealthy,
    canMakeRequest,
    nextAvailableTime,
    isLoading: query.isLoading,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isFetching: query.isFetching,
    isRefetching: query.isRefetching,
    error: query.error as Error | null,
    refetch: query.refetch,
    remove: query.remove,
  };
}

/**
 * 清除特定股票缓存的Hook
 */
export function useClearStockCache() {
  return useMutation(
    (code: string) => stockDataApi.clearCache(code),
    {
      onSuccess: (_, code) => {
        clearStockCache(code);
      },
    }
  );
}

/**
 * 批量获取股票行情数据的Hook - 支持大量股票的分批处理
 * @param codes 股票代码数组
 * @param options 查询选项
 */
export function useBatchStockQuotes(
  codes: string[],
  options: {
    enabled?: boolean;
    refetchInterval?: number;
  } = {}
) {
  const { enabled = true, refetchInterval } = options;

  return useQuery(
    ['stock-quotes', codes.join(',')],
    async () => {
      if (codes.length === 0) return { results: {}, errors: {} };

      // 批量获取股票行情 - 支持分批处理
      const results: Record<string, any> = {};
      const errors: Record<string, string> = {};

      // 设置并发限制，避免同时发起过多请求
      const CONCURRENT_LIMIT = 10;
      const BATCH_SIZE = 20;

      console.log(`批量获取股票行情：${codes.length}个股票`);

      // 分批处理股票代码
      const processBatch = async (batchCodes: string[]) => {
        const batchPromises = batchCodes.map(async (code) => {
          try {
            const quote = await stockDataApi.getStockQuote(code);
            results[code] = quote;
          } catch (error) {
            errors[code] = error instanceof Error ? error.message : '获取失败';
          }
        });

        await Promise.all(batchPromises);
      };

      // 将股票代码分成批次
      const batches: string[][] = [];
      for (let i = 0; i < codes.length; i += BATCH_SIZE) {
        batches.push(codes.slice(i, i + BATCH_SIZE));
      }

      // 限制并发批次数量
      for (let i = 0; i < batches.length; i += CONCURRENT_LIMIT) {
        const concurrentBatches = batches.slice(i, i + CONCURRENT_LIMIT);
        const batchPromises = concurrentBatches.map(batch => processBatch(batch));

        await Promise.all(batchPromises);

        // 批次间添加小延迟，避免API频率限制
        if (i + CONCURRENT_LIMIT < batches.length) {
          await new Promise(resolve => setTimeout(resolve, 50));
        }
      }

      console.log(`股票行情获取完成：成功${Object.keys(results).length}个，失败${Object.keys(errors).length}个`);

      return { results, errors };
    },
    {
      enabled: enabled && codes.length > 0,
      refetchInterval: refetchInterval,
      staleTime: 30000, // 30秒内认为数据是新鲜的
      cacheTime: 60000, // 缓存1分钟
    }
  );
}
